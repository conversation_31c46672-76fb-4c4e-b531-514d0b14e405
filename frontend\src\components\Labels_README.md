# Labels Feature Documentation

## Tổng quan
Chức năng Labels cho phép người dùng tạo và quản lý nhãn màu sắc để phân loại và tổ chức các task trong board, tương tự như trên taskboard.com.

## Các Component

### 1. LabelBadge
Component hiển thị label dạng badge với màu sắc.

**Props:**
- `label`: Object chứa thông tin label (id, name, color)
- `size`: Kích thước badge ('small', 'default')
- `showClose`: Hiển thị nút đóng để xóa label
- `onClose`: Callback khi click nút đóng
- `onClick`: Callback khi click vào badge
- `style`: Custom style
- `className`: Custom CSS class

**Sử dụng:**
```jsx
<LabelBadge 
  label={{ id: '1', name: 'Bug', color: '#ff4d4f' }}
  showClose={true}
  onClose={(label) => handleRemoveLabel(label)}
/>
```

### 2. LabelSelector
Component để chọn và gán labels cho task, hiển thị dạng popover.

**Props:**
- `taskId`: ID của task
- `boardId`: ID của board
- `taskLabels`: Danh sách labels hiện tại của task
- `onLabelsChange`: Callback khi labels thay đổi
- `trigger`: Trigger để mở popover ('click', 'hover')
- `placement`: Vị trí hiển thị popover
- `children`: Element trigger (optional)

**Tính năng:**
- Tìm kiếm labels
- Checkbox để chọn/bỏ chọn labels
- Tạo label mới
- Mở Label Manager

**Sử dụng:**
```jsx
<LabelSelector
  taskId={task.id}
  boardId={boardId}
  taskLabels={taskLabels}
  onLabelsChange={handleLabelsChange}
>
  <Button icon={<TagOutlined />}>Labels</Button>
</LabelSelector>
```

### 3. LabelManager
Component quản lý labels của board (tạo, sửa, xóa).

**Props:**
- `visible`: Hiển thị modal
- `onClose`: Callback khi đóng modal
- `boardId`: ID của board

**Tính năng:**
- Tạo label mới với tên và màu sắc
- Chỉnh sửa label inline
- Xóa label với xác nhận
- Color picker với màu sắc gợi ý

**Sử dụng:**
```jsx
<LabelManager
  visible={labelManagerVisible}
  onClose={() => setLabelManagerVisible(false)}
  boardId={selectedBoard}
/>
```

## Tích hợp vào các Component khác

### TaskCard
- Hiển thị labels của task dạng badges
- Nút để mở LabelSelector
- Auto-fetch labels khi component mount

### TaskEditModal
- Section quản lý labels trong form
- Hiển thị labels hiện tại với nút xóa
- LabelSelector để thêm labels mới

### DashBoard
- Nút "Labels" trong header để mở LabelManager
- Quản lý state của LabelManager modal

## API Endpoints

### Backend Routes (labelRoutes.js)
- `POST /labels` - Tạo label mới
- `GET /labels/board/:boardId` - Lấy labels của board
- `GET /labels/:labelId` - Lấy thông tin label
- `PUT /labels/:labelId` - Cập nhật label
- `DELETE /labels/:labelId` - Xóa label
- `POST /labels/add-label-to-task` - Gán label cho task
- `POST /labels/remove-label-from-task` - Xóa label khỏi task
- `GET /labels/task/:taskId` - Lấy labels của task

### Frontend Service (labelService.js)
```javascript
import labelService from '../services/labelService';

// Tạo label
await labelService.createLabel(boardId, name, color);

// Lấy labels của board
const labels = await labelService.getLabelsByBoardId(boardId);

// Cập nhật label
await labelService.updateLabel(labelId, name, color);

// Xóa label
await labelService.deleteLabel(labelId);

// Gán label cho task
await labelService.addLabelToTask(taskId, labelId);

// Xóa label khỏi task
await labelService.removeLabelFromTask(taskId, labelId);

// Lấy labels của task
const taskLabels = await labelService.getLabelsByTaskId(taskId);
```

## Styling

### CSS Classes
- `.label-badge` - Style cho badge
- `.task-labels` - Container cho labels trong task
- `.label-selector-popover` - Style cho popover
- Responsive design cho mobile

### Màu sắc mặc định
```javascript
const defaultColors = [
  '#f50', '#2db7f5', '#87d068', '#108ee9', '#f56a00',
  '#eb2f96', '#52c41a', '#13c2c2', '#1890ff', '#722ed1',
  '#fa541c', '#faad14', '#a0d911', '#36cfc9', '#40a9ff'
];
```

## Workflow sử dụng

1. **Tạo Labels**: Admin/Owner vào Board → Click nút "Labels" → Tạo labels với tên và màu
2. **Gán Labels cho Task**: 
   - Trong TaskCard: Click icon tag → Chọn labels
   - Trong TaskEditModal: Section Labels → Chọn/xóa labels
3. **Quản lý Labels**: Board header → Nút "Labels" → Sửa/xóa labels

## Lưu ý kỹ thuật

- Labels được lưu theo boardId, mỗi board có labels riêng
- Relationship: Board → Labels, Task ← TaskLabel → Label
- Auto-refresh labels khi có thay đổi
- Debounced search trong LabelSelector
- Color picker với presets và custom colors
- Responsive design cho mobile devices

## Troubleshooting

1. **Labels không hiển thị**: Kiểm tra boardId và API response
2. **Không thể tạo label**: Kiểm tra quyền user và validation
3. **Performance**: Labels được cache, chỉ fetch khi cần thiết
4. **Color picker**: Hỗ trợ hex colors, fallback về màu mặc định
