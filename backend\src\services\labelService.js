const { Label, TaskLabel } = require("../models");

class LabelService {
    //Manage Labels
    async createLabel(boardId, name, color) {
        return await Label.create({
            name,
            color,
            boardId,
        });
    }

    async getLabelsByBoardId(boardId) {
        return await Label.findAll({
            where: { boardId },
        });
    }

    async getLabelById(labelId) {
        return await Label.findByPk(labelId);
    }

    async deleteLabel(labelId) {
        return await Label.destroy({
            where: { id: labelId },
        });
    }

    //Manage Task Labels
    async addLabelToTask(taskId, labelId) {
        return await TaskLabel.create({
            taskId,
            labelId,
        });
    }

    async removeLabelFromTask(taskId, labelId) {
        return await TaskLabel.destroy({
            where: { taskId, labelId },
        });
    }

    async getLabelsByTaskId(taskId) {
        return await TaskLabel.findAll({
            where: { taskId },
            include: [
                {
                    model: Label,
                    as: "labels",
                },
            ],
        });
    }
}

module.exports = new LabelService();
