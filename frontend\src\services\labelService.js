import axios from "../utils/axiosCustomize";

const createLabel = async (boardId, name, color) => {
  const response = await axios.post(`/labels`, { boardId, name, color });
  return response.data;
};

const getLabelsByBoardId = async (boardId) => {
  const response = await axios.get(`/labels/board/${boardId}`);
  return response.data;
};

const getLabelById = async (labelId) => {
  const response = await axios.get(`/labels/${labelId}`);
  return response.data;
};

const deleteLabel = async (labelId) => {
  const response = await axios.delete(`/labels/${labelId}`);
  return response.data;
};

const addLabelToTask = async (taskId, labelId) => {
  const response = await axios.post(`/labels/add-label-to-task`, {
    taskId,
    labelId,
  });
  return response.data;
};

const removeLabelFromTask = async (taskId, labelId) => {
  const response = await axios.post(`/labels/remove-label-from-task`, {
    taskId,
    labelId,
  });
  return response.data;
};

const getLabelsByTaskId = async (taskId) => {
  const response = await axios.get(`/labels/task/${taskId}`);
  return response.data;
};

export default {
  createLabel,
  getLabelsByBoardId,
  getLabelById,
  deleteLabel,
  addLabelToTask,
  removeLabelFromTask,
  getLabelsByTaskId,
};
