// models/Label.js
module.exports = (sequelize, DataTypes) => {
    const Label = sequelize.define(
        "Label",
        {
            id: {
                type: DataTypes.UUID,
                defaultValue: DataTypes.UUIDV4,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            color: {
                type: DataTypes.STRING,
                allowNull: false,
                defaultValue: "#000000",
            },
            boardId: {
                type: DataTypes.UUID,
                allowNull: false,
            },
        },
        {
            tableName: "labels",
            timestamps: false,
        }
    );

    Label.associate = function (models) {
        Label.belongsTo(models.Board, {
            foreignKey: "board_id",
            as: "board",
        });
        Label.hasMany(models.TaskLabel, {
            foreignKey: "label_id",
            as: "taskLabels",
            onDelete: "CASCADE",
        });
    };

    return Label;
};
