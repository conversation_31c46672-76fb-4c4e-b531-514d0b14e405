import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import {
    Modal,
    Form,
    Input,
    Select,
    DatePicker,
    Upload,
    Button,
    message,
    List,
    Typography,
    Space
} from 'antd';
import {
    UploadOutlined,
    DeleteOutlined,
    DownloadOutlined,
    PaperClipOutlined,
    EyeOutlined
} from '@ant-design/icons';
import attachmentService from '../services/attachmentService';
import useMessage from '../hooks/useMessage';

const TaskEditModal = ({
    visible,
    task,
    listId,
    boardId,
    onClose,
    onUpdateTask,
    isChildTask,
    boardMembers = null,
    searchText,
    setSearchResult
}) => {
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState([]);
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [uploading, setUploading] = useState(false);
    const [saving, setSaving] = useState(false);
    const { showMessage, contextHolder } = useMessage();

    // Đồng bộ form khi task thay đổi hoặc modal mở
    useEffect(() => {
        if (visible && task) {
            form.setFieldsValue({
                title: task.title,
                notes: task.notes,
                due: task.due ? dayjs(task.due) : null,
                priority: task.priority
            });

            // Tải danh sách file đính kèm khi modal mở
            loadAttachments();
            // Reset danh sách file đã chọn
            setSelectedFiles([]);
        }
    }, [visible, task, form]);

    const loadAttachments = async () => {
        try {
            if (!task || !task.id) return;

            const response = await attachmentService.getAttachments(task.id);
            // API trả về dữ liệu trong response.data
            const attachments = Array.isArray(response.data)
                ? response.data
                : [];

            const filesWithUid = attachments.map((file) => ({
                ...file,
                id: file.id,
                fileName: file.fileName,
                mimeType: file.mimeType,
                webContentLink: file.webContentLink,
                webViewLink: file.webViewLink,
                fileSize: file.fileSize
            }));

            setFileList(filesWithUid);
        } catch (error) {
            console.error('Error loading file list:', error);
            message.error('Không thể tải danh sách tệp đính kèm');
        }
    };

    // Hàm tải lên tất cả file đã chọn
    const uploadAllFiles = async () => {
        if (selectedFiles.length === 0) return;

        setUploading(true);
        const formData = new FormData();
        selectedFiles.forEach((file) => {
            const fileToUpload = file.originFileObj || file;
            formData.append('files', fileToUpload);
        });

        try {
            const response = await attachmentService.uploadAttachment(
                task.id,
                formData
            );

            if (response.success) {
                showMessage(
                    'success',
                    `Đã tải lên thành công ${selectedFiles.length} tệp`
                );
                await loadAttachments();
            } else {
                showMessage(
                    'error',
                    response.message || 'Tải lên tệp thất bại'
                );
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            showMessage('error', 'Không thể tải lên tệp');
        } finally {
            setSelectedFiles([]);
            setUploading(false);
        }
    };

    const handleModalOk = async () => {
        try {
            setSaving(true);
            const values = await form.validateFields();
            const taskUpdate = {};

            // Check and update title
            if (values.title !== task.title) {
                taskUpdate.title = values.title;
            }

            // Check and update notes
            const currentNotes = task.notes || '';
            if (values.notes !== currentNotes) {
                taskUpdate.notes = values.notes;
            }

            // Check and update due date
            const currentDateStr = task.due
                ? dayjs(task.due).format('YYYY-MM-DD HH:mm')
                : null;
            const newDateStr = values.due
                ? values.due.format('YYYY-MM-DD HH:mm')
                : null;

            if (newDateStr !== currentDateStr) {
                taskUpdate.due = values.due;
            }

            // Check and update priority
            if (values.priority !== task.priority) {
                taskUpdate.priority = values.priority;
            }

            // Tải lên các file đã chọn trước
            await uploadAllFiles();

            // Gửi cập nhật nếu có thay đổi
            if (Object.keys(taskUpdate).length > 0 && onUpdateTask) {
                await onUpdateTask(listId, task.id, taskUpdate);
            }

            // Sau khi update task thành công, nếu đang search thì gọi lại search
            if (searchText && searchText.trim() && setSearchResult && boardId) {
                // Import động để tránh lỗi vòng lặp
                const TaskService = (await import('../services/taskService'))
                    .default;
                try {
                    const res = await TaskService.searchTasks(
                        searchText,
                        boardId
                    );
                    setSearchResult(res.data || []);
                } catch (err) {
                    // Nếu lỗi thì không cập nhật searchResult
                }
            }
            onClose();
        } catch (error) {
            console.error('Error saving task:', error);
            showMessage('error', 'Đã xảy ra lỗi khi lưu task');
        } finally {
            setSaving(false);
        }
    };

    const handleModalCancel = () => {
        if (saving) return; // Don't allow closing while saving
        onClose();
        form.resetFields();
        setSelectedFiles([]);
    };

    const handleFileSelect = ({ fileList: newFileList }) => {
        // Only store selected files, not uploaded yet
        setSelectedFiles(newFileList.map((file) => file.originFileObj || file));
    };

    const handleRemoveFile = async (file) => {
        try {
            await attachmentService.deleteAttachment(task.id, file.id);
            showMessage('success', 'Tệp đã được xóa thành công');
            await loadAttachments();
        } catch (error) {
            console.error('Error deleting file:', error);
            showMessage('error', 'Không thể xóa tệp');
        }
    };

    const handleDownloadFile = (file) => {
        if (file.webContentLink) {
            window.open(file.webContentLink, '_blank');
        } else {
            message.warning('Không có liên kết tải xuống cho tệp này');
        }
    };

    const handlePreviewFile = (file) => {
        if (file.webViewLink) {
            window.open(file.webViewLink, '_blank');
        } else {
            message.warning('Không thể xem trước tệp này');
        }
    };

    const uploadProps = {
        multiple: true,
        fileList: selectedFiles,
        onChange: handleFileSelect,
        beforeUpload: (file) => {
            // Prevent automatic upload, only add to waiting list
            return false;
        },
        showUploadList: true,
        progress: {
            strokeColor: {
                '0%': '#108ee9',
                '100%': '#87d068'
            },
            strokeWidth: 3,
            format: (percent) => `${parseFloat(percent.toFixed(2))}%`
        }
    };

    return (
        <Modal
            title='Chỉnh sửa task'
            open={visible}
            onOk={handleModalOk}
            onCancel={handleModalCancel}
            okText='Lưu'
            cancelText='Hủy'
            width={600}
            confirmLoading={saving}
            cancelButtonProps={{ disabled: saving }}
        >
            <Form
                form={form}
                layout='vertical'
                initialValues={{
                    title: task.title,
                    notes: task.notes || '',
                    due: task.due ? dayjs(task.due) : null,
                    priority: task.priority || 'low'
                }}
            >
                <Form.Item
                    label='Tiêu đề'
                    name='title'
                    rules={[
                        {
                            required: true,
                            message: 'Vui lòng nhập tiêu đề task!'
                        }
                    ]}
                >
                    <Input placeholder='Nhập tiêu đề task...' />
                </Form.Item>

                <Form.Item label='Mô tả' name='notes'>
                    <Input.TextArea
                        placeholder='Nhập mô tả task...'
                        autoSize={{ minRows: 3, maxRows: 6 }}
                    />
                </Form.Item>

                <Form.Item label='Độ ưu tiên' name='priority'>
                    <Select placeholder='Chọn độ ưu tiên'>
                        <Select.Option value='low'>Thấp</Select.Option>
                        <Select.Option value='medium'>Trung bình</Select.Option>
                        <Select.Option value='high'>Cao</Select.Option>
                    </Select>
                </Form.Item>

                <Form.Item label='Ngày hết hạn' name='due'>
                    <DatePicker
                        showTime={{ format: 'HH:mm' }}
                        style={{ width: '100%' }}
                        placeholder='Chọn ngày hết hạn và thời gian'
                        format='DD/MM/YYYY HH:mm'
                        allowClear
                    />
                </Form.Item>

                <Form.Item label='Tệp đính kèm'>
                    <Upload {...uploadProps}>
                        <Button
                            icon={<UploadOutlined />}
                            disabled={uploading || saving}
                        >
                            {uploading
                                ? 'Đang tải lên...'
                                : 'Chọn tệp để tải lên'}
                        </Button>
                    </Upload>

                    {selectedFiles.length > 0 && (
                        <div style={{ marginTop: 8, marginBottom: 16 }}>
                            <Typography.Text type='secondary'>
                                {saving
                                    ? 'Đang lưu Task và tải lên tệp...'
                                    : `${selectedFiles.length} tệp đã chọn. Tệp sẽ được tải lên khi bạn nhấn Lưu.`}
                            </Typography.Text>
                        </div>
                    )}

                    <div style={{ marginTop: 16 }}>
                        <Typography.Text strong>
                            Tệp đã tải lên:
                        </Typography.Text>
                    </div>

                    {fileList.length > 0 ? (
                        <List
                            size='small'
                            style={{ marginTop: 8 }}
                            bordered
                            dataSource={fileList}
                            renderItem={(item) => (
                                <List.Item
                                    actions={[
                                        <Button
                                            key='view'
                                            type='text'
                                            icon={<EyeOutlined />}
                                            onClick={() =>
                                                handlePreviewFile(item)
                                            }
                                            title='Xem tệp'
                                            disabled={!item.webViewLink}
                                        />,
                                        <Button
                                            key='download'
                                            type='text'
                                            icon={<DownloadOutlined />}
                                            onClick={() =>
                                                handleDownloadFile(item)
                                            }
                                            title='Tải xuống'
                                            disabled={!item.webContentLink}
                                        />,
                                        <Button
                                            key='delete'
                                            type='text'
                                            danger
                                            icon={<DeleteOutlined />}
                                            onClick={() =>
                                                handleRemoveFile(item)
                                            }
                                            title='Xóa tệp'
                                        />
                                    ]}
                                >
                                    <List.Item.Meta
                                        avatar={<PaperClipOutlined />}
                                        title={item.fileName}
                                        description={
                                            <>
                                                {item.fileSize
                                                    ? `Kích thước: ${(
                                                          item.fileSize / 1024
                                                      ).toFixed(2)} KB`
                                                    : 'Không có thông tin kích thước'}
                                            </>
                                        }
                                    />
                                </List.Item>
                            )}
                        />
                    ) : (
                        <Typography.Text
                            type='secondary'
                            style={{ display: 'block', marginTop: 8 }}
                        >
                            Chưa có tệp nào được tải lên
                        </Typography.Text>
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default TaskEditModal;
