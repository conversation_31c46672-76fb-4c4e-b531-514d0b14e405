const express = require("express");
const router = express.Router();
const { handleTokenExpiration } = require("../middleware/googleAuth");
const {
  createLabel,
  getLabelsByBoardId,
  getLabelById,
  deleteLabel,
  addLabelToTask,
  removeLabelFromTask,
  getLabelsByTaskId,
} = require("../controllers/labelController");
const { isAuthenticated } = require("../middleware/auth");

//Router manage label
router.post("/", isAuthenticated, handleTokenExpiration, createLabel);
router.get(
  "/board/:boardId",
  isAuthenticated,
  handleTokenExpiration,
  getLabelsByBoardId
);
router.get("/:labelId", isAuthenticated, handleTokenExpiration, getLabelById);
router.delete("/:labelId", isAuthenticated, handleTokenExpiration, deleteLabel);

//Router manage task label
router.post(
  "/add-label-to-task",
  isAuthenticated,
  handleTokenExpiration,
  addLabelToTask
);
router.post(
  "/remove-label-from-task",
  isAuthenticated,
  handleTokenExpiration,
  removeLabelFromTask
);
router.get(
  "/task/:taskId",
  isAuthenticated,
  handleTokenExpiration,
  getLabelsByTaskId
);

module.exports = router;
