import React, { useState, useEffect, useCallback } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  List,
  Space,
  Popconfirm,
  message,
  ColorPicker,
  Divider,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import labelService from "../services/labelService";
import LabelBadge from "./LabelBadge";

const LabelManager = ({ visible, onClose, boardId }) => {
  const [labels, setLabels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [form] = Form.useForm();
  const [createForm] = Form.useForm();
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Màu sắc mặc định cho labels
  const defaultColors = [
    "#f50",
    "#2db7f5",
    "#87d068",
    "#108ee9",
    "#f56a00",
    "#eb2f96",
    "#52c41a",
    "#13c2c2",
    "#1890ff",
    "#722ed1",
    "#fa541c",
    "#faad14",
    "#a0d911",
    "#36cfc9",
    "#40a9ff",
  ];

  useEffect(() => {
    if (visible && boardId) {
      fetchLabels();
    }
  }, [visible, boardId]);

  const fetchLabels = useCallback(async () => {
    try {
      setLoading(true);
      const response = await labelService.getLabelsByBoardId(boardId);
      setLabels(response.data || []);
    } catch (error) {
      message.error("Không thể tải danh sách labels");
      console.error("Error fetching labels:", error);
    } finally {
      setLoading(false);
    }
  }, [boardId]);

  const handleCreateLabel = async (values) => {
    try {
      const colorValue =
        typeof values.color === "string"
          ? values.color
          : values.color?.toHexString?.() || "#1890ff";

      await labelService.createLabel(boardId, values.name, colorValue);
      message.success("Tạo label thành công");
      createForm.resetFields();
      setShowCreateForm(false);
      fetchLabels();
    } catch (error) {
      message.error("Không thể tạo label");
      console.error("Error creating label:", error);
    }
  };

  const handleDeleteLabel = async (labelId) => {
    try {
      await labelService.deleteLabel(labelId);
      message.success("Xóa label thành công");
      fetchLabels();
    } catch (error) {
      message.error("Không thể xóa label");
      console.error("Error deleting label:", error);
    }
  };

  const handleEditLabel = (label) => {
    setEditingId(label.id);
    form.setFieldsValue({
      name: label.name,
      color: label.color,
    });
  };

  const handleSaveEdit = async (labelId) => {
    try {
      const values = await form.validateFields();
      const colorValue =
        typeof values.color === "string"
          ? values.color
          : values.color?.toHexString?.() || "#1890ff";

      await labelService.updateLabel(labelId, values.name, colorValue);
      message.success("Cập nhật label thành công");
      setEditingId(null);
      fetchLabels();
    } catch (error) {
      message.error("Không thể cập nhật label");
      console.error("Error updating label:", error);
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    form.resetFields();
  };

  const renderLabelItem = (label) => {
    const isEditing = editingId === label.id;

    if (isEditing) {
      return (
        <List.Item>
          <div style={{ width: "100%" }}>
            <Form form={form} layout="inline" style={{ width: "100%" }}>
              <Form.Item
                name="name"
                style={{ flex: 1 }}
                rules={[{ required: true, message: "Vui lòng nhập tên label" }]}
              >
                <Input placeholder="Tên label" />
              </Form.Item>
              <Form.Item name="color">
                <ColorPicker
                  presets={[{ label: "Recommended", colors: defaultColors }]}
                  showText
                />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    size="small"
                    onClick={() => handleSaveEdit(label.id)}
                  />
                  <Button
                    icon={<CloseOutlined />}
                    size="small"
                    onClick={handleCancelEdit}
                  />
                </Space>
              </Form.Item>
            </Form>
          </div>
        </List.Item>
      );
    }

    return (
      <List.Item
        actions={[
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditLabel(label)}
          />,
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa label này?"
            onConfirm={() => handleDeleteLabel(label.id)}
            okText="Xóa"
            cancelText="Hủy"
          >
            <Button type="text" icon={<DeleteOutlined />} size="small" danger />
          </Popconfirm>,
        ]}
      >
        <LabelBadge label={label} />
      </List.Item>
    );
  };

  return (
    <Modal
      title="Quản lý Labels"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <div style={{ marginBottom: 16 }}>
        {!showCreateForm ? (
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={() => setShowCreateForm(true)}
            block
          >
            Tạo label mới
          </Button>
        ) : (
          <Form
            form={createForm}
            onFinish={handleCreateLabel}
            layout="inline"
            style={{ width: "100%" }}
          >
            <Form.Item
              name="name"
              style={{ flex: 1 }}
              rules={[{ required: true, message: "Vui lòng nhập tên label" }]}
            >
              <Input placeholder="Tên label" />
            </Form.Item>
            <Form.Item name="color" initialValue="#1890ff">
              <ColorPicker
                presets={[{ label: "Recommended", colors: defaultColors }]}
                showText
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                />
                <Button
                  icon={<CloseOutlined />}
                  onClick={() => {
                    setShowCreateForm(false);
                    createForm.resetFields();
                  }}
                />
              </Space>
            </Form.Item>
          </Form>
        )}
      </div>

      <Divider />

      <List
        loading={loading}
        dataSource={labels}
        renderItem={renderLabelItem}
        locale={{ emptyText: "Chưa có label nào" }}
      />
    </Modal>
  );
};

export default LabelManager;
